{% extends 'Hr/base_hr.html' %}
{% load static %}

{% block title %}{{ page_title }} - نظام الدولية{% endblock %}

{% block page_title %}{{ page_title }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{% url 'accounts:home' %}">الرئيسية</a></li>
<li class="breadcrumb-item"><a href="{% url 'Hr:dashboard' %}">الموظفين</a></li>
<li class="breadcrumb-item active">{{ page_title }}</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">قائمة بنود الرواتب</h5>
        <a href="{% url 'Hr:salaries:salary_item_create' %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة بند جديد
        </a>
    </div>
    <div class="card-body">
        {% if salary_items %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم البند</th>
                        <th>نوع البند</th>
                        <th>طريقة الحساب</th>
                        <th>يؤثر على الإجمالي</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in salary_items %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ item.name }}</td>
                        <td>{{ item.get_item_type_display }}</td>
                        <td>{{ item.get_calculation_method_display }}</td>
                        <td>
                            {% if item.affects_total %}
                            <span class="badge bg-success">نعم</span>
                            {% else %}
                            <span class="badge bg-danger">لا</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{% url 'Hr:salaries:salary_item_edit' item.id %}" class="btn btn-sm btn-info">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <a href="{% url 'Hr:salaries:salary_item_delete' item.id %}" class="btn btn-sm btn-danger">
                                <i class="fas fa-trash"></i> حذف
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            لا توجد بنود رواتب مضافة حتى الآن. <a href="{% url 'Hr:salaries:salary_item_create' %}">أضف بند جديد</a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
